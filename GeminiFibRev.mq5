//+------------------------------------------------------------------+
//|                                           Fibonacci Reversal EA |
//|                          Fibonacci-based reversal trading strategy |
//|                                               VERSION 2.0 (FIXED) |
//+------------------------------------------------------------------+
#property version     "2.0"
#property description "FIXED VERSION: Aligned fractal and stop-loss logic with onurFibo.py"

#define FibReversalMagic 9765427

#include <Trade\Trade.mqh>
#include <Trade\SymbolInfo.mqh>
#include <Trade\PositionInfo.mqh>
#include <Trade\AccountInfo.mqh>

// --- Strategy parameters (Aligned with Python script)
input double InpPriceChangePercentage = 3.0;      // Price change threshold percentage
input double InpRiskPercent         = 2.0;       // Risk percentage per trade
input double InpFixedLotSize        = 0.01;      // Fixed lot size
input bool   InpUseFixedLotSize     = false;     // Use fixed lot size instead of risk percentage
// --- REMOVED: InpHardStopPercentage as it's now dynamic based on fractals

// Trading phases
enum ENUM_TRADING_PHASE
{
   PHASE_PASSIVE,       // Fibonacci detection phase
   PHASE_ACTIVE_LONG,   // Active phase searching for long entries
   PHASE_ACTIVE_SHORT   // Active phase searching for short entries
};

// --- Global variables
CTrade m_trade;
CSymbolInfo m_symbol;
CPositionInfo m_position;
CAccountInfo m_account;

ENUM_TRADING_PHASE m_tradingPhase = PHASE_PASSIVE;

// Fibonacci structure
double m_fib_236 = 0;
double m_fib_500 = 0;

// Locked peak and trough info
double m_lockedPeakPrice = 0;
datetime m_lockedPeakTime = 0;
double m_lockedTroughPrice = 0;
datetime m_lockedTroughTime = 0;

//+------------------------------------------------------------------+
//| FIX: Custom Fractal Detection Functions to Match Python          |
//| A fractal requires 2 bars on each side to be lower/higher.       |
//| We check the bar at shift = 2, as it has 2 bars before and after.|
//+------------------------------------------------------------------+
bool IsHighFractal(int shift)
{
   if(shift < 2) return false; // Not enough bars to the right for a full fractal

   MqlRates rates[];
   if(CopyRates(Symbol(), Period(), shift, 5, rates) < 5)
      return false; // Ensure we have 5 bars of data

   // rates[2] is the middle bar we are checking
   if(rates[2].high > rates[0].high && rates[2].high > rates[1].high &&
      rates[2].high > rates[3].high && rates[2].high > rates[4].high)
   {
      return true;
   }
   return false;
}

bool IsLowFractal(int shift)
{
   if(shift < 2) return false; // Not enough bars to the right

   MqlRates rates[];
   if(CopyRates(Symbol(), Period(), shift, 5, rates) < 5)
      return false;

   // rates[2] is the middle bar we are checking
   if(rates[2].low < rates[0].low && rates[2].low < rates[1].low &&
      rates[2].low < rates[3].low && rates[2].low < rates[4].low)
   {
      return true;
   }
   return false;
}


//+------------------------------------------------------------------+
//| Calculate Lot Size                                               |
//+------------------------------------------------------------------+
double CalculateLotSize(double stopLossPrice)
{
   if(InpUseFixedLotSize)
   {
      return(InpFixedLotSize);
   }

   m_account.InfoInteger(ACCOUNT_LEVERAGE);
   double balance = m_account.InfoDouble(ACCOUNT_BALANCE);
   double riskAmount = balance * (InpRiskPercent / 100.0);
   m_symbol.Name(Symbol());
   m_symbol.RefreshRates();
   double entryPrice = m_symbol.Ask(); // Assuming a buy for calculation purposes
   
   double stopLossPips = MathAbs(entryPrice - stopLossPrice);
   if(stopLossPips == 0) return 0.01; // Avoid division by zero

   double tickValue = m_symbol.TickValue();
   double tickSize = m_symbol.TickSize();

   if(tickSize == 0 || tickValue == 0) return 0.01; // Avoid division by zero

   double lotSize = riskAmount / (stopLossPips / tickSize * tickValue);

   // Normalize and check against limits
   double minLot = m_symbol.LotsMin();
   double maxLot = m_symbol.LotsMax();
   double stepLot = m_symbol.LotsStep();

   lotSize = MathFloor(lotSize / stepLot) * stepLot;

   if(lotSize < minLot) lotSize = minLot;
   if(lotSize > maxLot) lotSize = maxLot;

   return(lotSize);
}

//+------------------------------------------------------------------+
//| Reset all strategy variables to passive state                    |
//+------------------------------------------------------------------+
void ResetStrategy()
{
   Print("Strategy Resetting. Returning to Passive Phase.");
   m_tradingPhase = PHASE_PASSIVE;
   m_lockedPeakPrice = 0;
   m_lockedPeakTime = 0;
   m_lockedTroughPrice = 0;
   m_lockedTroughTime = 0;
   m_fib_236 = 0;
   m_fib_500 = 0;
}

//+------------------------------------------------------------------+
//| Process the active trading phase (look for entries)              |
//+------------------------------------------------------------------+
bool ProcessActivePhase()
{
   MqlRates currentBar[1];
   if(CopyRates(Symbol(), Period(), 0, 1, currentBar) <= 0)
   {
      Print("Error copying rates: ", GetLastError());
      return false;
   }
   double closePrice = currentBar[0].close;

   // Check if price has moved out of the Fib range - if so, reset
   if(closePrice > m_lockedPeakPrice || closePrice < m_lockedTroughPrice)
   {
      Print("Price moved outside of locked peak/trough. Resetting. Close:", closePrice, " Peak:", m_lockedPeakPrice, " Trough:", m_lockedTroughPrice);
      ResetStrategy();
      return false;
   }

   // --- LONG ENTRY ---
   if(m_tradingPhase == PHASE_ACTIVE_LONG)
   {
      if(closePrice > m_fib_236)
      {
         Print("BULLISH ACTIVATION: Close > Fib 236. C:", closePrice, " F:", m_fib_236);
         // --- FIX: Stop loss is now the locked trough price
         double sl = m_lockedTroughPrice;
         double tp = m_fib_500;
         double lots = CalculateLotSize(sl);

         if(lots > 0 && m_trade.Buy(lots, Symbol(), 0, sl, tp, "Long Entry"))
         {
            Print("Long position opened successfully, Ticket: ", m_trade.ResultDeal());
            return true;
         }
         else
         {
            Print("Error opening long position: ", m_trade.ResultRetcode(), " - ", m_trade.ResultComment());
            ResetStrategy(); // Reset on failure
         }
      }
   }
   // --- SHORT ENTRY ---
   else if(m_tradingPhase == PHASE_ACTIVE_SHORT)
   {
      if(closePrice < m_fib_236)
      {
         Print("BEARISH ACTIVATION: Close < Fib 236. C:", closePrice, " F:", m_fib_236);
         // --- FIX: Stop loss is now the locked peak price
         double sl = m_lockedPeakPrice;
         double tp = m_fib_500;
         double lots = CalculateLotSize(sl);

         if(lots > 0 && m_trade.Sell(lots, Symbol(), 0, sl, tp, "Short Entry"))
         {
            Print("Short position opened successfully, Ticket: ", m_trade.ResultDeal());
            return true;
         }
         else
         {
            Print("Error opening short position: ", m_trade.ResultRetcode(), " - ", m_trade.ResultComment());
            ResetStrategy(); // Reset on failure
         }
      }
   }
   return false;
}

//+------------------------------------------------------------------+
//| Process passive phase (Fibonacci detection)                      |
//+------------------------------------------------------------------+
bool ProcessPassivePhase()
{
   // We look for a fractal at bar index 2 (the most recently completed full fractal pattern)
   int shift = 2;

   MqlRates rates[3];
   if(CopyRates(Symbol(), Period(), shift, 3, rates) < 3) return false;
   
   double high = rates[0].high;
   double low = rates[0].low;
   datetime time = rates[0].time;

   // --- CHECK FOR BULLISH FIB (looking for a peak first, then a trough) ---
   if(m_lockedPeakPrice == 0) // We need a peak first
   {
       if(IsHighFractal(shift))
       {
           m_lockedPeakPrice = high;
           m_lockedPeakTime = time;
           Print("Initial Peak locked at: ", m_lockedPeakPrice, " at time ", TimeToString(m_lockedPeakTime));
       }
   }
   else // We have a peak, now we look for a trough to form the Fib
   {
       if(high > m_lockedPeakPrice) // A new higher high is made, reset and use this new peak
       {
           m_lockedPeakPrice = high;
           m_lockedPeakTime = time;
           m_lockedTroughPrice = 0; // Reset trough search
           Print("New Higher Peak locked at: ", m_lockedPeakPrice);
           return false;
       }

       if(IsLowFractal(shift))
       {
           double potentialTroughPrice = low;
           double diff = m_lockedPeakPrice - potentialTroughPrice;
           double changePercent = (diff / potentialTroughPrice) * 100.0;

           if(changePercent >= InpPriceChangePercentage)
           {
               m_lockedTroughPrice = potentialTroughPrice;
               m_lockedTroughTime = time;
               
               // Bullish Fibonacci found, calculate levels
               m_fib_236 = m_lockedTroughPrice + 0.236 * diff;
               m_fib_500 = m_lockedTroughPrice + 0.5 * diff;
               m_tradingPhase = PHASE_ACTIVE_LONG; // Switch to active long phase
               
               Print("BULLISH Fibonacci Pattern Found. Peak:", m_lockedPeakPrice, " Trough:", m_lockedTroughPrice);
               Print("Switching to ACTIVE_LONG. Fib 23.6:", m_fib_236, " Fib 50.0:", m_fib_500);
               return true;
           }
       }
   }

   // --- CHECK FOR BEARISH FIB (looking for a trough first, then a peak) ---
   if(m_lockedTroughPrice == 0) // We need a trough first
   {
       if(IsLowFractal(shift))
       {
           m_lockedTroughPrice = low;
           m_lockedTroughTime = time;
           Print("Initial Trough locked at: ", m_lockedTroughPrice, " at time ", TimeToString(m_lockedTroughTime));
       }
   }
   else // We have a trough, now we look for a peak to form the Fib
   {
       if(low < m_lockedTroughPrice) // A new lower low is made, reset and use this new trough
       {
           m_lockedTroughPrice = low;
           m_lockedTroughTime = time;
           m_lockedPeakPrice = 0; // Reset peak search
           Print("New Lower Trough locked at: ", m_lockedTroughPrice);
           return false;
       }

       if(IsHighFractal(shift))
       {
           double potentialPeakPrice = high;
           double diff = potentialPeakPrice - m_lockedTroughPrice;
           double changePercent = (diff / m_lockedTroughPrice) * 100.0;

           if(changePercent >= InpPriceChangePercentage)
           {
               m_lockedPeakPrice = potentialPeakPrice;
               m_lockedPeakTime = time;

               // Bearish Fibonacci found, calculate levels
               m_fib_236 = m_lockedPeakPrice - 0.236 * diff;
               m_fib_500 = m_lockedPeakPrice - 0.5 * diff;
               m_tradingPhase = PHASE_ACTIVE_SHORT; // Switch to active short phase
               
               Print("BEARISH Fibonacci Pattern Found. Trough:", m_lockedTroughPrice, " Peak:", m_lockedPeakPrice);
               Print("Switching to ACTIVE_SHORT. Fib 23.6:", m_fib_236, " Fib 50.0:", m_fib_500);
               return true;
           }
       }
   }
   
   return false;
}


//+------------------------------------------------------------------+
//| OnTick function, executed on every new tick                      |
//+------------------------------------------------------------------+
void OnTick()
{
    // Process only on a new bar
    static datetime lastBarTime = 0;
    datetime newBarTime = (datetime)SeriesInfoInteger(Symbol(), Period(), SERIES_LASTBAR_DATE);

    if(lastBarTime != newBarTime)
    {
        lastBarTime = newBarTime;

        // Check if there are any open positions
        if(PositionsTotal() == 0)
        {
             if(m_tradingPhase == PHASE_PASSIVE)
             {
                ProcessPassivePhase();
             }
             else // Active phase
             {
                ProcessActivePhase();
             }
        }
    }
}

//+------------------------------------------------------------------+
//| OnInit function                                                  |
//+------------------------------------------------------------------+
int OnInit()
{
   m_trade.SetExpertMagicNumber(FibReversalMagic);
   m_trade.SetMarginMode();
   m_symbol.Name(Symbol());
   
   Print("Fibonacci Reversal EA v2.0 Initialized.");
   Print("Logic aligned with onurFibo.py");
   Print("--- Custom 5-bar fractal detection enabled.");
   Print("--- Dynamic Stop Loss at fractal peak/trough enabled.");
   
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| OnDeinit function                                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   Print("EA Deinitialized. Reason code: ", reason);
}