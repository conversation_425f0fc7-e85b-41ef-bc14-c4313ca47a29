//+------------------------------------------------------------------+
//|                                           Fibonacci Reversal EA |
//|                          Fibonacci-based reversal trading strategy |
//|                                               VERSION 3.0 (PYTHON ALIGNED) |
//+------------------------------------------------------------------+
#property version     "3.0"
#property description "PYTHON ALIGNED VERSION: Exact replication of onurFibo.py logic"

#define FibReversalMagic 9765427

#include <Trade\Trade.mqh>
#include <Trade\SymbolInfo.mqh>
#include <Trade\PositionInfo.mqh>
#include <Trade\AccountInfo.mqh>

// --- Strategy parameters (Aligned with Python script)
input double InpPriceChangePercentage = 3.0;      // MIN_YUZDE_FARK (Price change threshold percentage)
input double InpRiskPercent         = 2.0;       // Risk percentage per trade
input double InpFixedLotSize        = 0.01;      // Fixed lot size
input bool   InpUseFixedLotSize     = false;     // Use fixed lot size instead of risk percentage
input double InpHardStopPercentage  = 1.5;      // HARD_STOP_YUZDE (Hard stop percentage)

// Trading modes (matching Python script)
enum ENUM_TRADING_MODE
{
   MODE_LOOKING_FOR_BULLISH_FIB,    // "Yükseliş Fibi Arıyor"
   MODE_LOOKING_FOR_BEARISH_FIB     // "Düşüş Fibi Arıyor"
};

// Fibonacci structure for candidate and active patterns
struct FibonacciPattern
{
   string type;                     // "Yükseliş" or "Düşüş"
   double tepe_fiyat;              // Peak price
   datetime tepe_zaman;            // Peak time
   double dip_fiyat;               // Trough price
   datetime dip_zaman;             // Trough time
   double level_236;               // 23.6% Fibonacci level
   double level_500;               // 50% Fibonacci level
};

// --- Global variables
CTrade m_trade;
CSymbolInfo m_symbol;
CPositionInfo m_position;
CAccountInfo m_account;

ENUM_TRADING_MODE m_mode = MODE_LOOKING_FOR_BULLISH_FIB;

// Pattern structures
FibonacciPattern m_aktif_fib;       // Active Fibonacci pattern
FibonacciPattern m_aday_fib;        // Candidate Fibonacci pattern
bool m_has_aktif_fib = false;
bool m_has_aday_fib = false;

// Locked peak and trough tracking (matching Python variables)
double m_kilitli_tepe_fiyat = 0;
datetime m_kilitli_tepe_zaman = 0;
double m_kilitli_dip_fiyat = DBL_MAX;  // float('inf') equivalent
datetime m_kilitli_dip_zaman = 0;

//+------------------------------------------------------------------+
//| Initialize Fibonacci Pattern                                     |
//+------------------------------------------------------------------+
void InitFibPattern(FibonacciPattern &fib, double tepe_fiyat, datetime tepe_zaman,
                   double dip_fiyat, datetime dip_zaman, string fib_type)
{
   fib.type = fib_type;
   fib.tepe_fiyat = tepe_fiyat;
   fib.tepe_zaman = tepe_zaman;
   fib.dip_fiyat = dip_fiyat;
   fib.dip_zaman = dip_zaman;

   double diff = MathAbs(fib.tepe_fiyat - fib.dip_fiyat);

   if(fib.type == "Yükseliş")
   {
      fib.level_236 = fib.dip_fiyat + 0.236 * diff;
      fib.level_500 = fib.dip_fiyat + 0.5 * diff;
   }
   else // "Düşüş"
   {
      fib.level_236 = fib.tepe_fiyat - 0.236 * diff;
      fib.level_500 = fib.tepe_fiyat - 0.5 * diff;
   }
}

//+------------------------------------------------------------------+
//| Check 3-bar close pattern for mode switching (Python logic)     |
//+------------------------------------------------------------------+
bool CheckModeSwitch(int current_index)
{
   if(current_index < 2) return false;

   MqlRates rates[3];
   if(CopyRates(Symbol(), Period(), current_index-2, 3, rates) < 3)
      return false;

   double close0 = rates[0].close;  // i-2
   double close1 = rates[1].close;  // i-1
   double close2 = rates[2].close;  // i (current)

   // Check for bullish pattern: close0 > close1 > close2
   if(close0 > close1 && close1 > close2 && m_mode != MODE_LOOKING_FOR_BULLISH_FIB)
   {
      m_mode = MODE_LOOKING_FOR_BULLISH_FIB;
      // Lock peak from the 3-bar window
      double max_high = MathMax(MathMax(rates[0].high, rates[1].high), rates[2].high);
      for(int i = 0; i < 3; i++)
      {
         if(rates[i].high == max_high)
         {
            m_kilitli_tepe_fiyat = max_high;
            m_kilitli_tepe_zaman = rates[i].time;
            break;
         }
      }
      Print("Mode switched to BULLISH FIB SEARCH. Peak locked at: ", m_kilitli_tepe_fiyat);
      return true;
   }

   // Check for bearish pattern: close0 < close1 < close2
   if(close0 < close1 && close1 < close2 && m_mode != MODE_LOOKING_FOR_BEARISH_FIB)
   {
      m_mode = MODE_LOOKING_FOR_BEARISH_FIB;
      // Lock trough from the 3-bar window
      double min_low = MathMin(MathMin(rates[0].low, rates[1].low), rates[2].low);
      for(int i = 0; i < 3; i++)
      {
         if(rates[i].low == min_low)
         {
            m_kilitli_dip_fiyat = min_low;
            m_kilitli_dip_zaman = rates[i].time;
            break;
         }
      }
      Print("Mode switched to BEARISH FIB SEARCH. Trough locked at: ", m_kilitli_dip_fiyat);
      return true;
   }

   return false;
}


//+------------------------------------------------------------------+
//| Calculate Lot Size                                               |
//+------------------------------------------------------------------+
double CalculateLotSize(double stopLossPrice)
{
   if(InpUseFixedLotSize)
   {
      return(InpFixedLotSize);
   }

   m_account.InfoInteger(ACCOUNT_LEVERAGE);
   double balance = m_account.InfoDouble(ACCOUNT_BALANCE);
   double riskAmount = balance * (InpRiskPercent / 100.0);
   m_symbol.Name(Symbol());
   m_symbol.RefreshRates();
   double entryPrice = m_symbol.Ask(); // Assuming a buy for calculation purposes

   double stopLossPips = MathAbs(entryPrice - stopLossPrice);
   if(stopLossPips == 0) return 0.01; // Avoid division by zero

   double tickValue = m_symbol.TickValue();
   double tickSize = m_symbol.TickSize();

   if(tickSize == 0 || tickValue == 0) return 0.01; // Avoid division by zero

   double lotSize = riskAmount / (stopLossPips / tickSize * tickValue);

   // Normalize and check against limits
   double minLot = m_symbol.LotsMin();
   double maxLot = m_symbol.LotsMax();
   double stepLot = m_symbol.LotsStep();

   lotSize = MathFloor(lotSize / stepLot) * stepLot;

   if(lotSize < minLot) lotSize = minLot;
   if(lotSize > maxLot) lotSize = maxLot;

   return(lotSize);
}

//+------------------------------------------------------------------+
//| Process Active Fibonacci Pattern (Python aktif_fib logic)       |
//+------------------------------------------------------------------+
bool ProcessActiveFib(MqlRates &current_bar, datetime current_time)
{
   if(!m_has_aktif_fib) return false;

   bool success = false;
   bool fail = false;
   double kar_orani = 0;

   if(m_aktif_fib.type == "Yükseliş")
   {
      // Check hard stop first
      if(current_bar.low < m_aktif_fib.level_236 * (1 - InpHardStopPercentage / 100.0))
      {
         fail = true;
         kar_orani = -InpHardStopPercentage;
      }
      // Check take profit
      else if(current_bar.high >= m_aktif_fib.level_500)
      {
         success = true;
         kar_orani = (m_aktif_fib.level_500 - m_aktif_fib.level_236) / m_aktif_fib.level_236 * 100.0;
      }
   }
   else // "Düşüş"
   {
      // Check hard stop first
      if(current_bar.high > m_aktif_fib.level_236 * (1 + InpHardStopPercentage / 100.0))
      {
         fail = true;
         kar_orani = -InpHardStopPercentage;
      }
      // Check take profit
      else if(current_bar.low <= m_aktif_fib.level_500)
      {
         success = true;
         kar_orani = (m_aktif_fib.level_236 - m_aktif_fib.level_500) / m_aktif_fib.level_236 * 100.0;
      }
   }

   if(success || fail)
   {
      string event = success ? "Fib Tamamlandı" : "Hard Stop";
      Print("TRADE COMPLETED: ", event, " Type: ", m_aktif_fib.type, " P&L: ", kar_orani, "%");

      // Reset active fibonacci
      m_has_aktif_fib = false;
      return true;
   }

   return false;
}

//+------------------------------------------------------------------+
//| Process Candidate Fibonacci Pattern (Python aday_fib logic)     |
//+------------------------------------------------------------------+
bool ProcessCandidateFib(MqlRates &current_bar, datetime current_time)
{
   if(!m_has_aday_fib) return false;

   if(m_aday_fib.type == "Yükseliş")
   {
      // Check if price moved outside the range - reset candidate
      if(current_bar.close < m_aday_fib.dip_fiyat || current_bar.close > m_aday_fib.tepe_fiyat)
      {
         Print("Bullish candidate reset - price outside range. Close: ", current_bar.close);
         m_has_aday_fib = false;
         return false;
      }
      // Check activation - price crosses above Fib 23.6%
      else if(current_bar.close > m_aday_fib.level_236)
      {
         Print("BULLISH CANDIDATE ACTIVATED! Close: ", current_bar.close, " > Fib 236: ", m_aday_fib.level_236);
         m_aktif_fib = m_aday_fib;  // Promote candidate to active
         m_has_aktif_fib = true;
         m_has_aday_fib = false;
         return true;
      }
   }
   else // "Düşüş"
   {
      // Check if price moved outside the range - reset candidate
      if(current_bar.close > m_aday_fib.tepe_fiyat || current_bar.close < m_aday_fib.dip_fiyat)
      {
         Print("Bearish candidate reset - price outside range. Close: ", current_bar.close);
         m_has_aday_fib = false;
         return false;
      }
      // Check activation - price crosses below Fib 23.6%
      else if(current_bar.close < m_aday_fib.level_236)
      {
         Print("BEARISH CANDIDATE ACTIVATED! Close: ", current_bar.close, " < Fib 236: ", m_aday_fib.level_236);
         m_aktif_fib = m_aday_fib;  // Promote candidate to active
         m_has_aktif_fib = true;
         m_has_aday_fib = false;
         return true;
      }
   }

   return false;
}

//+------------------------------------------------------------------+
//| Process Fibonacci Pattern Detection (Python main logic)         |
//+------------------------------------------------------------------+
bool ProcessFibonacciDetection(MqlRates &current_bar, datetime current_time)
{
   if(m_mode == MODE_LOOKING_FOR_BULLISH_FIB)
   {
      // Update locked peak if current high is higher
      if(current_bar.high > m_kilitli_tepe_fiyat)
      {
         m_kilitli_tepe_fiyat = current_bar.high;
         m_kilitli_tepe_zaman = current_time;
         Print("New higher peak locked: ", m_kilitli_tepe_fiyat);
      }
      else if(m_kilitli_tepe_zaman != 0) // We have a locked peak
      {
         // Check if current low creates sufficient price difference
         double fark = (m_kilitli_tepe_fiyat - current_bar.low) / current_bar.low;
         if(fark >= InpPriceChangePercentage / 100.0)
         {
            // Create candidate Fibonacci pattern
            InitFibPattern(m_aday_fib, m_kilitli_tepe_fiyat, m_kilitli_tepe_zaman,
                          current_bar.low, current_time, "Yükseliş");
            m_has_aday_fib = true;

            Print("BULLISH CANDIDATE created. Peak: ", m_kilitli_tepe_fiyat,
                  " Trough: ", current_bar.low, " Diff: ", fark*100, "%");
            Print("Fib levels - 236: ", m_aday_fib.level_236, " 500: ", m_aday_fib.level_500);
            return true;
         }
      }
   }
   else // MODE_LOOKING_FOR_BEARISH_FIB
   {
      // Update locked trough if current low is lower
      if(current_bar.low < m_kilitli_dip_fiyat)
      {
         m_kilitli_dip_fiyat = current_bar.low;
         m_kilitli_dip_zaman = current_time;
         Print("New lower trough locked: ", m_kilitli_dip_fiyat);
      }
      else if(m_kilitli_dip_zaman != 0) // We have a locked trough
      {
         // Check if current high creates sufficient price difference
         double fark = (current_bar.high - m_kilitli_dip_fiyat) / m_kilitli_dip_fiyat;
         if(fark >= InpPriceChangePercentage / 100.0)
         {
            // Create candidate Fibonacci pattern
            InitFibPattern(m_aday_fib, current_bar.high, current_time,
                          m_kilitli_dip_fiyat, m_kilitli_dip_zaman, "Düşüş");
            m_has_aday_fib = true;

            Print("BEARISH CANDIDATE created. Trough: ", m_kilitli_dip_fiyat,
                  " Peak: ", current_bar.high, " Diff: ", fark*100, "%");
            Print("Fib levels - 236: ", m_aday_fib.level_236, " 500: ", m_aday_fib.level_500);
            return true;
         }
      }
   }

   return false;
}

//+------------------------------------------------------------------+
//| Main Strategy Processing (Python stratejiyi_calistir logic)     |
//+------------------------------------------------------------------+
void ProcessStrategy(int current_index)
{
   MqlRates current_bar[1];
   if(CopyRates(Symbol(), Period(), current_index, 1, current_bar) <= 0)
   {
      Print("Error copying current bar: ", GetLastError());
      return;
   }

   datetime current_time = current_bar[0].time;

   // Step 1: Check for mode switching (Python lines 48-55)
   CheckModeSwitch(current_index);

   // Step 2: Process active Fibonacci pattern if exists (Python lines 57-88)
   if(m_has_aktif_fib)
   {
      if(ProcessActiveFib(current_bar[0], current_time))
      {
         return; // Trade completed, continue to next bar
      }
      return; // Still in active trade, skip other processing
   }

   // Step 3: Process candidate Fibonacci pattern if exists (Python lines 90-103)
   if(m_has_aday_fib)
   {
      if(ProcessCandidateFib(current_bar[0], current_time))
      {
         return; // Candidate activated, continue to next bar
      }
      return; // Still monitoring candidate, skip pattern detection
   }

   // Step 4: Look for new Fibonacci patterns (Python lines 105-126)
   ProcessFibonacciDetection(current_bar[0], current_time);
}


//+------------------------------------------------------------------+
//| OnTick function, executed on every new tick                      |
//+------------------------------------------------------------------+
void OnTick()
{
    // Process only on a new bar (matching Python's bar-by-bar processing)
    static datetime lastBarTime = 0;
    datetime newBarTime = (datetime)SeriesInfoInteger(Symbol(), Period(), SERIES_LASTBAR_DATE);

    if(lastBarTime != newBarTime)
    {
        lastBarTime = newBarTime;

        // Get current bar index (0 = most recent completed bar)
        static int processedBars = 0;
        int totalBars = Bars(Symbol(), Period());

        // Process from bar 2 onwards (matching Python's range(2, len(df)))
        if(totalBars > processedBars && totalBars >= 3)
        {
            for(int i = MathMax(2, processedBars); i < totalBars; i++)
            {
                ProcessStrategy(totalBars - 1 - i); // Convert to shift from current
            }
            processedBars = totalBars;
        }
    }
}

//+------------------------------------------------------------------+
//| OnInit function                                                  |
//+------------------------------------------------------------------+
int OnInit()
{
   m_trade.SetExpertMagicNumber(FibReversalMagic);
   m_trade.SetMarginMode();
   m_symbol.Name(Symbol());

   // Initialize variables to match Python script initial state
   m_mode = MODE_LOOKING_FOR_BULLISH_FIB;
   m_has_aktif_fib = false;
   m_has_aday_fib = false;
   m_kilitli_tepe_fiyat = 0;
   m_kilitli_tepe_zaman = 0;
   m_kilitli_dip_fiyat = DBL_MAX;
   m_kilitli_dip_zaman = 0;

   Print("Fibonacci Reversal EA v3.0 Initialized - PYTHON ALIGNED");
   Print("Parameters: Price Change = ", InpPriceChangePercentage, "%, Hard Stop = ", InpHardStopPercentage, "%");
   Print("Mode: Looking for Bullish Fibonacci patterns");
   Print("Logic exactly matches onurFibo.py");

   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| OnDeinit function                                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   Print("EA Deinitialized. Reason code: ", reason);
}